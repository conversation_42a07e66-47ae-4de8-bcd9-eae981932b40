# Go Service Boilerplate

A production-ready Go service boilerplate that provides a solid foundation for building microservices with gRPC and HTTP support. This template includes essential features and best practices used across our organization.

## Features

- **Dual Protocol Support**:
  - gRPC server with protobuf definitions
  - HTTP router with public and API endpoints
- **Health Checks**: Built-in health check endpoints for both gRPC and HTTP
- **Clean Architecture**: Well-organized project structure following Go best practices
- **Database Migrations**: Built-in support for database migrations
- **Docker Support**: Docker and docker-compose configuration included

## Project Structure

```
.
├── cmd/                   # Application entry points
│   ├── grpc/              # gRPC server main
│   └── boilerplate/       # http server main
├── internal/              # Private application code
│   ├── server/            # Server Implementations
│   ├── router/            # HttpRoute Implementations
│   ├── responses/         # Response Objects Defn
│   ├── modules/           # Business logic modules
│   │   └── */migrations/  # Database migrations for each module
├── proto/                 # Protocol buffer definitions
│   ├── health/            # Health check service
│   └── ping/              # Ping service
├── docker-compose.yaml    # Docker compose configuration
├── .env.example          # Example environment variables
├── go.mod                 # Go module definition
├── go.sum                 # Go module checksums
└── Makefile                # Build automation
```

## Prerequisites

- Go 1.21 or later
- Protocol Buffers compiler
- Make
- Docker and Docker Compose (optional)

## Getting Started

1. Clone the repository:

   ```bash
   git clone https://github.com/your-org/oa-go-boilerplate.git
   cd oa-go-boilerplate
   ```

2. Install dependencies:

   ```bash
   go mod download
   ```

3. Copy the example environment file:

   ```bash
   cp .env.example .env
   ```

4. Run the server:
   ```bash
   go run cmd/grpc/main.go
   go run cmd/boilerplate/main.go
   ```

## Database Migrations

The project uses Goland Migrate to manage database schema changes. Migrations are organized by modules in the `internal/modules/<module_name>/migrations` directory.

### Creating a New Migration

To create a new migration file:

```bash
make migrate-create app_name=<module_name> file_name=<migration_name>
```

Example:

```bash
make migrate-create app_name=users file_name=create_users_table
```

This will create two new files in `internal/modules/users/migrations`:

- `XXXXXX_create_users_table.up.sql`
- `XXXXXX_create_users_table.down.sql`

### Applying Migrations

To apply migrations (move forward):

```bash
make migrate-up app_name=<module_name> uri="<database_uri>"
```

Example:

```bash
make migrate-up app_name=users uri="postgres://username:password@localhost:5432/dbname?sslmode=disable"
```

### Rolling Back Migrations

To rollback migrations (move backward):

```bash
make migrate-down app_name=<module_name> uri="<database_uri>"
```

Example:

```bash
make migrate-down app_name=users uri="postgres://username:password@localhost:5432/dbname?sslmode=disable"
```

## Contributing

Please follow the standard Go coding conventions and Git commit guidelines when contributing to this project.
