services:
  postgres:
    image: postgres:14.15-alpine
    hostname: postgres
    container_name: postgres
    ports:
      - 5432:5432
    restart: always
    shm_size: 128mb
    volumes:
      - ./tmp/pg-data:/var/lib/postgresql
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
      POSTGRES_DB: postgres
    networks:
      - boilerplate-network

  redis:
    image: bitnami/redis:7.4.0
    hostname: redis
    container_name: redis
    ports:
      - 6379:6379
    restart: always
    volumes:
      - ./tmp/redis-data:/bitnami/redis/data
    environment:
      REDIS_PASSWORD: password
    networks:
      - boilerplate-network

networks:
  boilerplate-network:
    driver: bridge
