SERVICE_NAME := boilerplate
MAIN_PACKAGE_PATH := ./cmd
DB_PATH := postgres://user:pass@localhost:5432/$(SERVICE_NAME)

## tidy: tidy the application
.PHONY: tidy
tidy:
	go mod tidy

## build: build the application
.PHONY: build
build:
	go build -tags=viper_bind_struct -o=./bin/${SERVICE_NAME} ${MAIN_PACKAGE_PATH}/${SERVICE_NAME}/*.go

## run: run the  application
.PHONY: run
run: build
	go run -tags=viper_bind_struct ${MAIN_PACKAGE_PATH}/${SERVICE_NAME}/*.go

## serve: run the application with hot reloading
.PHONY: serve
serve:
	export SERVICE_NAME=${SERVICE_NAME} && \
	air -c .air.toml

# Target for creating the database
.PHONY: create-database
create-database:
	@echo "Creating database $(SERVICE_NAME) if it doesn't exist..."
	@docker exec -e PGPASSWORD=pass postgres psql -U user -d postgres -tc "SELECT 1 FROM pg_database WHERE datname = '$(SERVICE_NAME)'" | grep -q 1 || docker exec -e PGPASSWORD=pass postgres psql -U user -d postgres -c "CREATE DATABASE $(SERVICE_NAME);"

# Target for creating a schema in PostgreSQL
.PHONY: create-schema
create-schema: create-database
	@if [ "$(app_name)" = "" ]; then \
		echo "Error: app_name must be specified. Usage: make create-schema app_name=<app_name>"; \
		exit 1; \
	fi
	@echo "Creating schema $(app_name) if it doesn't exist..."
	@docker exec -e PGPASSWORD=pass postgres psql -h postgres -U user -d $(SERVICE_NAME) -c "CREATE SCHEMA IF NOT EXISTS $(app_name);"

# Target for creating a migration
.PHONY: migrate-create
migrate-create:
	@if [ "$(app_name)" = "" ] || [ "$(file_name)" = "" ]; then \
		echo "Error: app_name and file_name must be specified. Usage: make migrate-create app_name=<app_name> file_name=<file_name>"; \
		exit 1; \
	fi
	migrate create -ext sql -dir internal/modules/$(app_name)/migrations $(file_name)

.PHONY: migrate-up
migrate-up: create-schema
	@if [ "$(app_name)" = "" ]; then \
		echo "Error: app_name must be specified. Usage: make migrate-up app_name=<app_name>"; \
		exit 1; \
	fi
	migrate -path internal/modules/$(app_name)/migrations -database "$(DB_PATH)?search_path=$(app_name)&sslmode=disable" -verbose up

.PHONY: migrate-down
migrate-down: create-schema
	@if [ "$(app_name)" = "" ]; then \
		echo "Error: app_name must be specified. Usage: make migrate-down app_name=<app_name>"; \
		exit 1; \
	fi
	migrate -path internal/modules/$(app_name)/migrations -database "$(DB_PATH)?search_path=$(app_name)&sslmode=disable" -verbose down

# Target for forcing migration version
.PHONY: migrate-force
migrate-force:
	@if [ "$(app_name)" = "" ] || [ "$(version)" = "" ]; then \
		echo "Error: app_name and version must be specified. Usage: make migrate-force app_name=<app_name> version=<version>"; \
		exit 1; \
	fi
	migrate -path internal/modules/$(app_name)/migrations -database "$(DB_PATH)?search_path=$(app_name)&sslmode=disable" force $(version)