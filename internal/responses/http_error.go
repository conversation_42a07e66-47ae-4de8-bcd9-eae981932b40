package response

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	oaerrors "github.com/oneassure-tech/oa-errors/go"
)

type HTTPError struct {
	Status  int16
	Problem *HttpProblem
}

func (h HTTPError) Error() string {
	return fmt.Sprint("An HTTP Error Occurred with Status Code : ", h.Status)
}

func NewHTTPError(Status int16, Problem *HttpProblem) *HTTPError {
	return &HTTPError{
		Status:  Status,
		Problem: Problem,
	}
}

type HttpProblem struct {
	Type           string      `json:"type"`
	Title          string      `json:"title"`
	Detail         string      `json:"detail"`
	Instance       string      `json:"instance"`
	TraceId        *string     `json:"request_id,omitempty"`
	AdditionalInfo interface{} `json:"additional_info,omitempty"`
}

func GenericHttpInternalServerError(ctx *gin.Context) *HTTPError {
	return NewHTTPError(
		http.StatusInternalServerError,
		&HttpProblem{
			Type:     oaerrors.ErrCodeUnexpected.GetCode(),
			Title:    oaerrors.ErrCodeUnexpected.GetMessage(),
			Instance: ctx.Request.URL.Path,
		},
	)
}

func GenericHttpNotFoundError(ctx *gin.Context) *HTTPError {
	return NewHTTPError(
		http.StatusNotFound,
		&HttpProblem{
			Type:     oaerrors.ErrCodeNotFound.GetCode(),
			Title:    oaerrors.ErrCodeNotFound.GetMessage(),
			Instance: ctx.Request.URL.Path,
		},
	)
}
