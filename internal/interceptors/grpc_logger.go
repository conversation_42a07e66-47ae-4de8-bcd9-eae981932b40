package interceptor

import (
	"context"
	"fmt"
	"path"
	"strings"

	"github.com/oneassure-tech/oa-go-boilerplate/internal/constant"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/handler"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/logger"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/telemetry"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// UnaryServerLoggerInterceptor returns a new unary server interceptor that adds scoped logging.
// The scope is determined by the service name from the gRPC method path.
func UnaryServerLoggerInterceptor() grpc.UnaryServerInterceptor {
	// Create metric service instance
	metricService := telemetry.NewMetricService()

	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, h grpc.UnaryHandler) (interface{}, error) {
		// Get base logger
		baseLogger := logger.GetLogger()

		methodPath := info.FullMethod
		// Extract service name from gRPC method path
		// Expected format: "/oa.v0.service_name.submodule_name/method"
		var scope string
		parts := strings.Split(path.Dir(methodPath), ".")
		scope = strings.ToLower(parts[3])

		// Add request context information to logger
		logger := baseLogger.GetScopedLogger(scope).With(
			zap.String("type", constant.GRPCRequest),
			zap.String("grpc_method", methodPath),
		)

		// Create handler context with logger
		handlerCtx := &handler.HandlerContext{
			Logger:  logger,
			GrpcCtx: ctx,
			Req:     req,
		}

		// Add handler context to gRPC context
		newCtx := context.WithValue(ctx, constant.RouterContext, handlerCtx)

		// Handle the request
		resp, err := h(newCtx, req)

		if err != nil {
			st, ok := status.FromError(err)
			if !ok {
				st = status.New(codes.Unknown, err.Error())
			}

			// Log error with context
			// Log error details with proper formatting and context
			logger.Error(fmt.Sprintf("❌ gRPC Request Failed [%s]", path.Base(methodPath)),
				zap.String("status_code", st.Code().String()),
				zap.String("error_message", st.Message()),
				zap.Any("error_details", st.Details()),
			)

			// Increment error counter using metric service
			metricService.IncrementGRPCError(ctx,
				path.Base(info.FullMethod),
				info.FullMethod,
			)
			return nil, err
		}

		// Log success
		logger.Info(fmt.Sprintf("✅ gRPC Request Succeeded [%s]", path.Base(methodPath)))

		// Increment success counter using metric service
		metricService.IncrementGRPCSuccess(ctx,
			path.Base(info.FullMethod),
			info.FullMethod,
		)

		return resp, nil
	}
}
