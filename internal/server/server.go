// package server

// import (
// 	"log"

// 	healthpb "github.com/oneassure-tech/oa-go-boilerplate/proto/health/v1"
// 	pingpb "github.com/oneassure-tech/oa-go-boilerplate/proto/ping/v1"
// 	"google.golang.org/grpc"
// 	"google.golang.org/grpc/reflection"
// )

// func NewGrpcServer() *grpc.Server {
// 	grpcServer := grpc.NewServer()
// 	reflection.Register(grpcServer)

// 	// Register all services
// 	if err := RegisterServices(grpcServer); err != nil {
// 		log.Fatalf("failed to register services: %v", err)
// 	}

// 	return grpcServer

// }

// func RegisterServices(s *grpc.Server) error {
// 	// Register the health service
// 	healthpb.RegisterHealthServiceServer(s, newHealthServer())

// 	// Register the ping service
// 	pingpb.RegisterPingServiceServer(s, newPingServer())

// 	return nil
// }

package server

import (
	"fmt"
	"net/http"

	"github.com/oneassure-tech/oa-go-boilerplate/internal/http_router"
)

type HttpServer struct {
	Port   uint16
	Router http_router.RouterIface
}

type ServerInputParams struct {
	Port   uint16
	Router http_router.RouterIface
}

// func (s *HttpServer) SetGlobalVersion(version string) {
// 	s.Router.GlobalVersion = version
// }

func NewServer(params *ServerInputParams) *HttpServer {
	return &HttpServer{
		Port:   params.Port,
		Router: params.Router,
	}
}

func (s *HttpServer) Run() error {
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", s.Port),
		Handler: s.Router.GetEngine(),
	}

	return server.ListenAndServe()
}
