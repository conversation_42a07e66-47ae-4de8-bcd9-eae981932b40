package handler

import (
	"context"

	"github.com/oneassure-tech/oa-go-boilerplate/internal/constant"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/logger"
	response "github.com/oneassure-tech/oa-go-boilerplate/internal/responses"
)

// CustomHandler is a function type for handling requests with our custom context
type CustomHandler func(ctx *HandlerContext) (*response.SuccessResponse, *response.ErrorResponse)

// OptionStruct defines the configuration for a handler
type OptionStruct struct {
	Handler           CustomHandler
	RequireValidation bool
	DTOStruct         interface{}
	OptionType        string
}

// GetHandlerOptionsInputParams defines the input parameters for GetHandlerOptions
type GetHandlerOptionsInputParams struct {
	Handler           CustomHandler
	RequireValidation bool
	DTOStruct         interface{}
}

// MustEmbedHandler provides common functionality for handlers
type MustEmbedHandler struct {
}

// NoOp is a no-operation function to satisfy the interface
func (m *MustEmbedHandler) NoOp() {}

// GetHandlerOptions creates an OptionStruct with the provided handler configuration
func (m *MustEmbedHandler) GetHandlerOptions(g *GetHandlerOptionsInputParams) *OptionStruct {
	return &OptionStruct{
		Handler:           g.Handler,
		RequireValidation: g.RequireValidation,
		DTOStruct:         g.DTOStruct,
	}
}

// AdaptGrpcToGenericHandler adapts a gRPC request to our generic handler format
func (m *MustEmbedHandler) AdaptGrpcToGenericHandler(ctx context.Context, fn CustomHandler) (*response.SuccessResponse, error) {
	log := logger.GetLogger().GetScopedLogger("handler")
	// Extract the handler context that was injected by the interceptor
	handlerCtxInterface := ctx.Value(constant.RouterContext)
	if handlerCtxInterface == nil {
		log.Error("Failed to extract handler context in AdaptGrpcToGenericHandler")
		return nil, response.NewInternalServerError().TransformToGrpcError()
	}

	handlerCtx, ok := handlerCtxInterface.(*HandlerContext)
	if !ok {
		log.Error("Invalid handler context type in AdaptGrpcToGenericHandler")
		return nil, response.NewInternalServerError().TransformToGrpcError()
	}

	resp, err := fn(handlerCtx)

	if err != nil {
		return nil, err.TransformToGrpcError()
	}

	return resp, nil
}
