// Package constant provides application-wide constants
package constant

// Request context constants
const (
	// ApiGroup identifies the current API group/submodule name in context
	ApiGroup = "apiGroup"
	// RouterContext identifies our router context in request context
	RouterContext = "routerContext"
	// Module identifies the current module name in context
	Module = "module"
)

// Request types
const (
	HTTPRequest = "http"
	GRPCRequest = "grpc"
)

// Request validation types
const (
	Body   = "body"
	Params = "params"
	Query  = "query"
)

// Common field names
const (
	Password      = "password"
	Token         = "token"
	Secret        = "secret"
	Authorization = "authorization"
	Cookie        = "cookie"
)

// Common Router Types
const (
	HTTP_PUBLIC = "public"

	GRPC_INTERNAL = "internal"
)
