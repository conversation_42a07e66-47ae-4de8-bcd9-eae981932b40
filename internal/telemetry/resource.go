package telemetry

import (
	"github.com/oneassure-tech/oa-go-boilerplate/internal/config"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
)

// NewResource creates a new OpenTelemetry resource with service information.
// This resource is used to identify the service in telemetry data and includes
// standard OpenTelemetry resource attributes like service name and environment.
func NewResource() (*resource.Resource, error) {
	// Get application configuration to access service details
	cfg := config.GetConfig()

	// Create a new resource with standard OpenTelemetry attributes
	// These attributes help identify the source of telemetry data
	return resource.Merge(
		resource.Default(), // Start with default resource attributes
		resource.NewWithAttributes(
			semconv.SchemaURL, // Use the latest OpenTelemetry schema
			// Set service name for identification in telemetry systems
			semconv.ServiceName(cfg.SvcName),
			// Set environment (dev, staging, prod) for context
			semconv.DeploymentEnvironment(cfg.Environment),
		),
	)
}
