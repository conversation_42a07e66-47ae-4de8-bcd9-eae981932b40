package logger

import (
	"encoding/json"
	"fmt"
	"log"
	"sync"

	"github.com/oneassure-tech/oa-go-boilerplate/internal/config"
	"go.opentelemetry.io/contrib/bridges/otelzap"
	"go.opentelemetry.io/otel/log/global"
	"go.uber.org/zap"
)

// Logger represents our custom logger with scope management
type Logger struct {
	*zap.Logger
	scopes map[string]*Logger
	mu     *sync.RWMutex
}

var (
	_Logger *Logger
	once    sync.Once
)

// With creates a child logger and adds structured context to it
func (l *Logger) With(fields ...zap.Field) *Logger {
	return &Logger{
		Logger: l.Logger.With(fields...),
		scopes: l.scopes,
		mu:     l.mu,
	}
}

// Error logs a message at error level with automatic stack trace
func (l *Logger) Error(msg string, fields ...zap.Field) {
	// Add stack trace field if not already present
	hasStacktrace := false
	for _, field := range fields {
		if field.Key == "stacktrace" {
			hasStacktrace = true
			break
		}
	}
	if !hasStacktrace {
		fields = append(fields, zap.StackSkip("stacktrace", 1))
	}
	l.Logger.Error(msg, fields...)
}

func getCommonLogFields() []zap.Field {
	return []zap.Field{
		zap.String("svc_name", config.GetConfig().SvcName),
		zap.String("environment", config.GetConfig().Environment),
	}
}

// GetLogger returns the singleton logger instance
func GetLogger() *Logger {
	if _Logger != nil {
		return _Logger
	}
	panic("Logger not initialized")
}

// GetScopedLogger returns a logger for a specific scope
// If the scope doesn't exist, it creates a new one
func (l *Logger) GetScopedLogger(scope string) *Logger {

	cfg := config.GetConfig()
	// In dev mode, just return the base logger
	if cfg.Environment == config.Dev {
		return l
	}

	l.mu.RLock()
	if logger, exists := l.scopes[scope]; exists {
		l.mu.RUnlock()
		return logger
	}
	l.mu.RUnlock()

	// If we get here, we need to create a new scoped logger
	l.mu.Lock()
	defer l.mu.Unlock()

	// Double-check after acquiring write lock
	if logger, exists := l.scopes[scope]; exists {
		return logger
	}

	// Create scoped name in format: service_name.scope
	scopedName := fmt.Sprintf("%s.%s", cfg.SvcName, scope)

	// Create new scoped logger with OpenTelemetry core
	l.scopes[scope] = &Logger{
		Logger: zap.New(otelzap.NewCore(
			scopedName, // Use scoped name for OpenTelemetry
			otelzap.WithLoggerProvider(global.GetLoggerProvider()),
		)),
	}

	l.scopes[scope].Logger = l.scopes[scope].Logger.With(
		getCommonLogFields()...,
	)

	return l.scopes[scope]
}

// GetBaseLogger returns the base logger without any scope
func (l *Logger) GetBaseLogger() *Logger {
	return l
}

// NewLogger initializes the logger singleton
func NewLogger() {
	once.Do(func() {
		cfg := config.GetConfig()
		var err error
		var baseLogger *zap.Logger

		if cfg.Environment != config.Dev {
			// No Op
		} else {
			// In dev mode, use standard zap development logger
			rawJSON := []byte(`{
				"level": "debug",
				"encoding": "console",
				"outputPaths": ["stdout"],
				"errorOutputPaths": ["stderr"],
				"encoderConfig": {
				  "messageKey": "message",
				  "levelKey": "level",
				  "levelEncoder": "uppercase",
				  "stacktracekey": "stacktrace",
				  "timeKey": "ts",
				  "timeEncoder": "iso8601"
				}
			  }`)

			var zapCfg zap.Config
			if err := json.Unmarshal(rawJSON, &zapCfg); err != nil {
				log.Fatalf("Failed to unmarshal zap config: %v", err)
				panic(err)
			}

			baseLogger, err = zapCfg.Build(
				zap.AddStacktrace(zap.ErrorLevel),
				zap.Fields(
					getCommonLogFields()...,
				),
			)

			if err != nil {
				log.Fatalf("Failed to build dev zap logger: %v", err)
				panic(err)
			}
		}

		_Logger = &Logger{
			Logger: baseLogger,
			scopes: make(map[string]*Logger),
			mu:     &sync.RWMutex{},
		}
	})
}
