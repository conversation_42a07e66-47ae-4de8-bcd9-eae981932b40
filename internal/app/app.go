package app

import (
	"github.com/oneassure-tech/oa-go-boilerplate/internal/config"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/grpc"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/http_router"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/logger"
)

// "github.com/oneassure-tech/oa-go-boilerplate/internal/router"

type AppContext struct {
	Router     map[string]http_router.RouterIface
	GrpcRouter map[string]grpc.RouterIface
	Config     config.Config
	Logger     *logger.Logger
	// Tracer
}

type AppContextInputParams struct {
	Router     map[string]http_router.RouterIface
	GrpcRouter map[string]grpc.RouterIface
}

func NewAppContext(params *AppContextInputParams) *AppContext {
	return &AppContext{
		Router:     params.Router,
		GrpcRouter: params.GrpcRouter,
	}
}
