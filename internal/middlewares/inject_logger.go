package middleware

// import (
// 	"github.com/gin-gonic/gin"
// 	"github.com/oneassure-tech/oa-go-boilerplate/internal/constant"
// 	"github.com/oneassure-tech/oa-go-boilerplate/internal/http_router"
// 	"github.com/oneassure-tech/oa-go-boilerplate/internal/logger"
// 	"go.uber.org/zap"
// )

// // InjectLogger middleware adds a scoped logger to the request context.
// // The scope is determined by the API group (submodule) handling the request.
// func InjectLogger(ctx *gin.Context) {
// 	// Get base logger
// 	baseLogger := logger.GetLogger()

// 	// Extract the request context
// 	reqCtx := http_router.ExtractRequestContext(ctx)

// 	// Add request context information to logger
// 	logger := baseLogger.With(
// 		zap.String("type", constant.HTTPRequest),
// 		zap.String("http_path", ctx.Request.URL.Path),
// 		zap.String("http_method", ctx.Request.Method),
// 		zap.String("http_client_ip", ctx.ClientIP()),
// 	)

// 	// Add query string parameters if present
// 	if len(ctx.Request.URL.RawQuery) > 0 {
// 		logger = logger.With(
// 			zap.String("http_query_string", ctx.Request.URL.RawQuery),
// 		)
// 	}

// 	// Add non-sensitive headers
// 	headers := make(map[string]string)
// 	for k, v := range ctx.Request.Header {
// 		// Skip sensitive headers
// 		if k != constant.Authorization && k != constant.Cookie {
// 			headers[k] = v[0] // Take first value if multiple exists
// 		}
// 	}
// 	if len(headers) > 0 {
// 		logger = logger.With(
// 			zap.Any("http_headers", headers),
// 		)
// 	}

// 	// Add request body for non-GET requests
// 	if ctx.Request.Method != "GET" {
// 		var jsonBody map[string]interface{}
// 		http_router.ExtractHttpRequestBody(ctx, &jsonBody)

// 		if len(jsonBody) > 0 {
// 			// Remove sensitive fields
// 			delete(jsonBody, constant.Password)
// 			delete(jsonBody, constant.Token)
// 			delete(jsonBody, constant.Secret)

// 			logger = logger.With(
// 				zap.Any("http_body", jsonBody),
// 			)
// 		}
// 	}

// 	// Update the logger in the request context
// 	reqCtx.Logger = logger

// 	ctx.Next()
// }
