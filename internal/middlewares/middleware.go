package middleware

import (
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/config"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func InitMiddlewares(g *gin.RouterGroup) {

	publicCorsCfg := cors.DefaultConfig()
	publicCorsCfg.AllowOrigins = config.GetConfig().CorsOrigins

	g.Use(cors.New(publicCorsCfg))
	g.Use(otelgin.Middleware("otel-otlp-go-service"))

	// g.Use(InjectLogger)
}
