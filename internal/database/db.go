package database

import (
	"fmt"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type DatabaseIface interface {
	Init() *gorm.DB
}

type Database struct {
	Dsn    string
	Schema string
}

type DbInputParams struct {
	Dsn    string
	Schema string
}

func NewDatabase(params *DbInputParams) *Database {
	return &Database{
		Dsn:    params.Dsn,
		Schema: params.Schema,
	}
}

// Initialize a new database connection
func (d *Database) Init() *gorm.DB {
	dsnWithSchema := fmt.Sprintf("%s search_path=%s", d.Dsn, d.Schema)
	db, err := gorm.Open(postgres.Open(dsnWithSchema), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}
	// fmt.Printf("SET search_path TO %s ", d.Schema)

	// db.Exec("SET search_path TO " + d.<PERSON>hem<PERSON>)

	return db
}
