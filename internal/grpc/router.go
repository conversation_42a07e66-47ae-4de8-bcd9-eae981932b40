package grpc

import (
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	interceptor "github.com/oneassure-tech/oa-go-boilerplate/internal/interceptors"
)

type Router struct {
	router *grpc.Server
}

func NewRouter() RouterIface {
	// Create server with interceptors
	server := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			interceptor.UnaryServerLoggerInterceptor(),
		),
	)

	return &Router{
		router: server,
	}
}

func (g *Router) GetRouter() *grpc.Server {
	return g.router
}

func (g *Router) EnableReflection() {
	reflection.Register(g.router)
}
