package module

import (
	"github.com/oneassure-tech/oa-go-boilerplate/internal/app"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/config"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/grpc"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/http_router"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/logger"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/modules/health"
)

type ModuleIface interface {
	Initalize()
	RegisterApps()
	CreateContext(map[string]http_router.RouterIface, map[string]grpc.RouterIface)
}

type Module struct {
	apps   []app.AppInitFn
	appCtx *app.AppContext
}

func New() ModuleIface {
	return &Module{}
}

// Register your apps here
func (m *Module) RegisterApps() {
	m.apps = []app.AppInitFn{
		// example.New,
		health.New,
	}
}

// Create the app context to be injected into apps
func (m *Module) CreateContext(routerMap map[string]http_router.RouterIface, grpcRouterMap map[string]grpc.RouterIface) {

	m.appCtx = &app.AppContext{
		Config:     config.GetConfig(),
		Router:     routerMap,
		GrpcRouter: grpcRouterMap,
		Logger:     logger.GetLogger().GetScopedLogger("initialization"),
	}
}

// Initialize your apps
func (m *Module) Initalize() {

	if m.apps == nil {
		panic("App initializers were not added")
	}

	// Loop over apps and initialize them
	for _, app := range m.apps {
		newApp := app()
		name := newApp.SetAppName()

		app().Initialize(name, m.appCtx)
	}
}
