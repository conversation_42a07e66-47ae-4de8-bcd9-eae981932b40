package v3

// import (
// 	response "github.com/oneassure-tech/oa-go-boilerplate/internal/responses"
// 	"github.com/oneassure-tech/oa-go-boilerplate/internal/router"
// )

// func (c *Handler) HealthCheck(ctx *router.CustomContext) (*response.SuccessResponse, *response.ErrorResponse) {
// 	// func HealthCheck(ctx *gin.Context, appCtx *app.AppContext) (*response.HTTPSuccess, error) {

// 	c.Svc.Do()

// 	return &response.SuccessResponse{
// 		Status:  200,
// 		Payload: "Hello World V3",
// 	}, nil
// }
