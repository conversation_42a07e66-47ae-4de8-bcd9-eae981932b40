package v2

import (
	"context"
	"fmt"

	oaerror "github.com/oneassure-tech/oa-errors/go"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/handler"
	response "github.com/oneassure-tech/oa-go-boilerplate/internal/responses"
	health "github.com/oneassure-tech/oa-protos/go/oa-boilerplate/v0/health"
	"go.uber.org/zap"
	epb "google.golang.org/genproto/googleapis/rpc/errdetails"
	"google.golang.org/grpc/codes"
)

func (h *Handler) HealthCheck(ctx *handler.HandlerContext) (*response.SuccessResponse, *response.ErrorResponse) {
	// Use the logger from context which already has request and scope information
	logger := ctx.Logger

	// return nil, response.NewInternalServerError()

	// Log entry to the function with request type
	if ctx.GinCtx != nil {
		logger.Info("Processing HTTP health check request",
			zap.String("handler_version", "v2"),
			zap.String("request_type", "http"))
	} else {
		logger.Info("Processing gRPC health check request",
			zap.String("handler_version", "v2"),
			zap.String("request_type", "grpc"))
	}

	if ctx.GinCtx != nil {
		// Log before service call
		logger.Debug("Calling health service",
			zap.String("service_method", "Do"),
			zap.String("service", "HealthService"))

		h.Svc.Do()

		// Log after service call
		logger.Info("Health check successful",
			zap.Int("status", 200),
			zap.String("response", "Hello World V2"))

		// return &response.SuccessResponse{
		// 	Status:  200,
		// 	Payload: "Hello World V2",
		// }, nil

		return nil, &response.ErrorResponse{
			Status: &response.Status{
				GrpcStatus:       int(codes.Internal),
				DownstreamStatus: func() *int32 { i := int32(504); return &i }(),
			},
			Problem: &response.Problem{
				Type:   oaerror.ErrDatabaseConnection.GetCode(),
				Title:  oaerror.ErrDatabaseConnection.GetMessage(),
				Detail: "Some Detail",
				AdditionalInfo: &epb.QuotaFailure{
					Violations: []*epb.QuotaFailure_Violation{{
						Subject:     fmt.Sprintf("name:%s", "Test"),
						Description: "Limit one greeting per person",
					}},
				},
			},
		}
	}

	// Log when skipping response
	logger.Warn("Skipping response for non-HTTP request",
		zap.String("handler_version", "v2"))

	// return nil, nil

	return nil, &response.ErrorResponse{
		Status: &response.Status{
			GrpcStatus: int(codes.Internal),
		},
		Problem: &response.Problem{
			Type:   "Another Type",
			Title:  "Some Title",
			Detail: "Some Detail",
			AdditionalInfo: &epb.QuotaFailure{
				Violations: []*epb.QuotaFailure_Violation{{
					Subject:     fmt.Sprintf("name:%s", "Test"),
					Description: "Limit one greeting per person",
				}},
			},
		},
	}

	// return nil, nil
}

func (g *GrpcHandler) Check(ctx context.Context, _ *health.HealthCheckRequest) (*health.HealthCheckResponse, error) {
	_, err := g.Handler.AdaptGrpcToGenericHandler(ctx, g.Handler.HealthCheck)

	if err != nil {
		return nil, err
	}

	return &health.HealthCheckResponse{
		DummyResponse: "Test Successful",
	}, nil
}
