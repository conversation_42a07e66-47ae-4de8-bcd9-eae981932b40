package v2

import (
	"github.com/oneassure-tech/oa-go-boilerplate/internal/handler"
	service "github.com/oneassure-tech/oa-go-boilerplate/internal/modules/health/services"
	health "github.com/oneassure-tech/oa-protos/go/oa-boilerplate/v0/health"
)

type Handler struct {
	handler.MustEmbedHandler
	Svc *service.HealthService
}

type GrpcHandler struct {
	health.UnimplementedHealthServer
	Handler *Handler
}
