package health

import (
	"context"

	"github.com/oneassure-tech/oa-go-boilerplate/internal/app"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/constant"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/handler"
	v2 "github.com/oneassure-tech/oa-go-boilerplate/internal/modules/health/handlers/v2"
	service "github.com/oneassure-tech/oa-go-boilerplate/internal/modules/health/services"
	"github.com/oneassure-tech/oa-protos/go/oa-boilerplate/v0/health"
)

type HealthApp struct {
	app.MustEmbedApp
}

// New is a constructor equivalent which initialises the HealthApp struct and returns a pointer to the object HealthApp
func New() app.AppIface {
	return &HealthApp{}
}

func (app *HealthApp) SetAppName() string {
	return "health"
}

func (app *HealthApp) Initialize(appName string, appContext *app.AppContext) {
	// Create a context for the primary api route for this submodule
	ctx := context.Background()
	// Set the API Group
	ctx = context.WithValue(ctx, constant.ApiGroup, appName)

	// Initialize your service,clients...
	svc := &service.HealthService{
		DB: "This is details for company",
	}

	// Initialize your controller/handler
	h2 := &v2.Handler{
		Svc: svc,
	}

	g2 := &v2.GrpcHandler{
		Handler: h2,
	}

	publicRouter := appContext.Router[constant.HTTP_PUBLIC]
	grpcRouter := appContext.GrpcRouter[constant.GRPC_INTERNAL]

	health.RegisterHealthServer(grpcRouter.GetRouter(), g2)

	// Attach the routes
	publicRouter.RegisterRoute(ctx, appName, "GET", "/",
		map[uint8]*handler.OptionStruct{
			2: h2.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h2.HealthCheck,
				RequireValidation: false,
			}),
		},
	)
	publicRouter.RegisterRoute(ctx, appName, "POST", "/",
		map[uint8]*handler.OptionStruct{
			2: h2.GetHandlerOptions(&handler.GetHandlerOptionsInputParams{
				Handler:           h2.HealthCheck,
				RequireValidation: false,
			}),
		},
	)
}
