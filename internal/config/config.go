package config

import "github.com/spf13/viper"

var _ViperConfig Config = &_viperConfig{}

const (
	dbDriver = "postgres"
	Dev      = "dev"
)

func New() Config {
	viper.SetDefault("database.driver", dbDriver)
	return _ViperConfig
}

func GetConfig() Config {
	if _ViperConfig != nil {
		return _ViperConfig
	}

	// Add a log line here
	panic("Config is not initilized")
}

type Config = *_viperConfig

type _viperConfig struct {
	MaxVersion        uint8    `mapstructure:"max_version"`
	MinVersion        uint8    `mapstructure:"min_version"`
	Environment       string   `mapstructure:"environment"`
	HttpPublicPort    uint16   `mapstructure:"http_public_port"`
	GrpcPort          uint16   `mapstructure:"grpc_port"`
	CorsOrigins       []string `mapstructure:"cors_origins"`
	SvcName           string   `mapstructure:"service_name"`
	OtlpCollectorGrpc string   `mapstructure:"otlp_collector_grpc"`
	OtlpCollectorHttp string   `mapstructure:"otlp_collector_http"`
	Database          struct {
		Driver   string `mapstructure:"driver"`
		Host     string `mapstructure:"host"`
		User     string `mapstructure:"user"`
		Password string `mapstructure:"pass"`
		Name     string `mapstructure:"name"`
		Port     string `mapstructure:"port"`
	} `mapstructure:"database"`
}
