package main

import (
	"github.com/oneassure-tech/oa-go-boilerplate/internal/config"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/constant"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/grpc"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/http_router"
	middleware "github.com/oneassure-tech/oa-go-boilerplate/internal/middlewares"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/server"
	"golang.org/x/sync/errgroup"
)

var httpServers []*server.HttpServer
var grpcServers []*server.GrpcServer

func initRouters() (map[string]http_router.RouterIface, map[string]grpc.RouterIface) {
	// Setup the public HTTP router
	publicRouter := http_router.NewHttpRouter(&http_router.RouterInputParams{
		MaxVersion: config.GetConfig().MaxVersion,
		MinVersion: config.GetConfig().MinVersion,
	})

	// Setup the global middlewares
	middleware.InitMiddlewares(publicRouter.GetGlobalRouteGroup())

	// Configure the HTTPServer
	publicSrv := server.NewServer(&server.ServerInputParams{
		Port:   config.GetConfig().HttpPublicPort,
		Router: publicRouter,
	})

	httpServers = append(httpServers, publicSrv)

	// Setup the GRPC Router
	grpcRouter := grpc.NewRouter()
	grpcRouter.EnableReflection()

	// Configure the GRPCRouter
	grpcSrv := server.NewGrpcServer(&server.GrpcServerInputParams{
		Port:   config.GetConfig().GrpcPort,
		Router: grpcRouter,
	})

	grpcServers = append(grpcServers, grpcSrv)

	return map[string]http_router.RouterIface{
			constant.HTTP_PUBLIC: publicRouter,
		}, map[string]grpc.RouterIface{
			constant.GRPC_INTERNAL: grpcRouter,
		}
}

func runServers(g *errgroup.Group) {

	// // Start the server
	// g.Go(func() error {
	// 	// Add a log line here
	// 	return publicSrv.Run()
	// })

	for _, srv := range httpServers {
		g.Go(func() error {
			return srv.Run()
		})
	}

	for _, srv := range grpcServers {
		g.Go(func() error {
			return srv.Run()
		})
	}
}
