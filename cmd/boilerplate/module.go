package main

import (
	"github.com/oneassure-tech/oa-go-boilerplate/internal/grpc"
	"github.com/oneassure-tech/oa-go-boilerplate/internal/http_router"
	module "github.com/oneassure-tech/oa-go-boilerplate/internal/modules"
)

func initModules(httpRouters map[string]http_router.RouterIface, grpcRouters map[string]grpc.RouterIface) {

	// Register subapps created inside the module
	m := module.New()

	m.RegisterApps()
	m.CreateContext(httpRouters, grpcRouters)
	m.Initalize()

}
