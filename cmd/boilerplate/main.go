package main

import (
	"context"
	"errors"
	"log"

	"github.com/oneassure-tech/oa-go-boilerplate/internal/logger"
	"golang.org/x/sync/errgroup"
)

func main() {
	// ctx := context.Background()

	// loggerProvider := opentel.NewLogProvider(ctx)

	// // Handle shutdown properly so nothing leaks.
	// defer func() {
	// 	if err := loggerProvider.Shutdown(ctx); err != nil {
	// 		fmt.Println(err)
	// 	}
	// }()

	// global.SetLoggerProvider(loggerProvider)

	// Initialize the config
	initConfig()

	logger.NewLogger()

	// Setup Telemetry
	otelShutdown, err := initOtel(context.Background())
	if err != nil {
		// Add a log line here
		panic(err)
	}
	// Handle telemetry shutdown properly so nothing leaks.
	defer func() {
		err = errors.Join(err, otelShutdown(context.Background()))
	}()

	var g errgroup.Group

	// Initialise the HTTP routers
	httpRouters, grpcRouters := initRouters()
	runServers(&g)

	// Initiliase the Modules of the service
	initModules(httpRouters, grpcRouters)

	if err := g.Wait(); err != nil {
		// Add a log line here
		log.Fatal(err)
	}
}
