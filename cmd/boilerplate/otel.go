package main

import (
	"context"
	"errors"

	"github.com/oneassure-tech/oa-go-boilerplate/internal/telemetry"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/log/global"
)

// initOtel initializes OpenTelemetry providers (metrics, logs) and returns a shutdown function
// that properly cleans up resources when called. The shutdown function should be deferred
// in the main function to ensure proper cleanup.
func initOtel(ctx context.Context) (shutdown func(context.Context) error, err error) {
	// shutdownFuncs holds all the cleanup functions that need to be called
	// when shutting down OpenTelemetry providers. Each provider (metrics, logs)
	// will add its own shutdown function to this slice.
	var shutdownFuncs []func(context.Context) error

	// shutdown is a closure that iterates through all registered shutdown functions,
	// executing each with the provided context. It combines any errors that occur
	// during shutdown using errors.Join and clears the shutdownFuncs slice to
	// prevent accidental double shutdowns.
	shutdown = func(ctx context.Context) error {
		// Initialize error as nil
		var err error
		// Iterate through all shutdown functions
		for _, fn := range shutdownFuncs {
			// Join any errors that occur during shutdown
			err = errors.Join(err, fn(ctx))
		}
		// Clear the shutdown functions to prevent double cleanup
		shutdownFuncs = nil
		return err
	}

	// handleErr is a helper function that combines initialization errors with
	// any errors that occur during partial shutdown. This ensures we don't lose
	// error information when cleaning up after a failed initialization.
	handleErr := func(inErr error) {
		// Join the input error with any errors that occur during shutdown
		err = errors.Join(inErr, shutdown(ctx))
	}

	// Create a new OpenTelemetry resource with service information.
	// This resource will be shared across all telemetry providers (metrics, logs)
	// to ensure consistent service identification and attributes.
	res, err := telemetry.NewResource()
	if err != nil {
		// If resource creation fails, combine the error with any cleanup errors
		// and return both the shutdown function (for cleanup) and the error.
		handleErr(err)
		return shutdown, err
	}

	// Set up meter provider with the shared resource.
	// The meter provider is responsible for creating meters that can record
	// measurements like counters, gauges, and histograms.
	meterProvider, err := telemetry.NewMeterProvider(res)
	if err != nil {
		// If meter provider creation fails, handle the error and return
		handleErr(err)
		return shutdown, err
	}
	// Register the meter provider's shutdown function for cleanup
	shutdownFuncs = append(shutdownFuncs, meterProvider.Shutdown)
	// Set the global meter provider for use throughout the application
	otel.SetMeterProvider(meterProvider)

	// Set up logger provider with the shared resource.
	// The logger provider creates loggers that will include service information
	// and other attributes from the shared resource in all log entries.
	loggerProvider, err := telemetry.NewLoggerProvider(res)
	if err != nil {
		// If logger provider creation fails, handle the error and return
		handleErr(err)
		return shutdown, err
	}
	// Register the logger provider's shutdown function for cleanup
	shutdownFuncs = append(shutdownFuncs, loggerProvider.Shutdown)
	// Set the global logger provider for use throughout the application
	global.SetLoggerProvider(loggerProvider)

	// Return the shutdown function and any errors that occurred during initialization
	return shutdown, err
}
